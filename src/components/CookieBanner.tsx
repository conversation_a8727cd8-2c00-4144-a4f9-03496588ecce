import { Modal } from "microapps";
import { playAudioWithFallback } from '../utils/audioUtils';

interface CookieBannerProps {
  onAccept: () => void;
}

/**
 * <PERSON>ie Banner Component
 *
 * Displays a cookie consent banner that appears at the start of the game.
 * Features:
 * - Clean, modern design
 * - Clear explanation of cookie usage
 * - Accept button to proceed to main menu
 */
export const CookieBanner: React.FC<CookieBannerProps> = ({ onAccept }) => {
  /**
   * Handle accept button click - play background music and proceed
   */
  const handleAcceptAndPlay = () => {
    // Play background music
    playAudioWithFallback(
      '/assets/sounds/sound.mp3',
      () => {
        console.log('🎵 Música de fondo terminada');
      },
      () => {
        console.log('🎵 Música de fondo iniciada');
      },
      (error) => {
        console.warn('⚠️ Error reproduciendo música de fondo:', error);
      }
    );

    // Proceed with the original accept action
    onAccept();
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '500px',
        width: '100%',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
        textAlign: 'center',
        animation: 'slideIn 0.3s ease-out'
      }}>
        {/* Cookie Icon */}
        <div style={{
          fontSize: '64px',
          marginBottom: '20px'
        }}>
          🍪
        </div>

        {/* Title */}
        <h2 style={{
          color: '#333',
          marginBottom: '16px',
          fontSize: '24px',
          fontWeight: '600'
        }}>
          ¡Bienvenido al Juego de Adivinanza!
        </h2>

        {/* Description */}
        <p style={{
          color: '#666',
          marginBottom: '24px',
          fontSize: '16px',
          lineHeight: '1.5'
        }}>
          Este juego utiliza cookies y almacenamiento local para mejorar tu experiencia de juego,
          guardar tu progreso y personalizar el contenido. Al continuar, aceptas el uso de estas tecnologías.
        </p>

        {/* Features list */}
        <div style={{
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px',
          textAlign: 'left'
        }}>
          <h4 style={{
            color: '#333',
            marginBottom: '12px',
            fontSize: '16px',
            fontWeight: '500'
          }}>
            ¿Qué incluye este juego?
          </h4>
          <ul style={{
            color: '#666',
            fontSize: '14px',
            margin: 0,
            paddingLeft: '20px'
          }}>
            <li>🎭 Personajes generados por IA</li>
            <li>🎤 Conversación por voz en tiempo real</li>
            <li>💡 Sistema de pistas inteligente</li>
            <li>📺 Integración con Movistar+</li>
            <li>🔍 Información detallada con Perplexity</li>
          </ul>
        </div>

        {/* Accept Button */}
        <button
          onClick={handleAcceptAndPlay}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '12px 32px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            width: '100%'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#0056b3';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#007bff';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          🚀 ¡Acepto y quiero jugar!
        </button>

        {/* Footer note */}
        <p style={{
          color: '#999',
          fontSize: '12px',
          marginTop: '16px',
          marginBottom: 0
        }}>
          Puedes gestionar tus preferencias en cualquier momento desde la configuración del navegador.
        </p>
      </div>

      {/* CSS Animation */}
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
      `}</style>
    </div>
  );
};
